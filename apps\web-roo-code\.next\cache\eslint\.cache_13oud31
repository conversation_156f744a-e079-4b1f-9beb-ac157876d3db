[{"C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\actions\\evals.ts": "1", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\enterprise\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\evals\\evals.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\evals\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\layout.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\privacy\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\shell.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\terms\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\animated-text.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\footer.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\index.ts": "12", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\nav-bar.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\stats-display.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\theme-toggle.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\enterprise\\contact-form.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\animated-background.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\code-example.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\company-logos.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\faq-section.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\features-mobile.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\features.tsx": "22", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\index.ts": "23", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\install-section.tsx": "24", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\testimonials-mobile.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\testimonials.tsx": "26", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\whats-new-button.tsx": "27", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\providers\\index.ts": "28", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\providers\\posthog-provider.tsx": "29", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\providers\\providers.tsx": "30", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\button.tsx": "31", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\chart.tsx": "32", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\index.ts": "33", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx": "34", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\scroll-button.tsx": "35", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\table.tsx": "36", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\constants.ts": "37", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\format-currency.ts": "38", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\format-duration.ts": "39", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\format-score.ts": "40", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\format-tokens.ts": "41", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\hooks\\index.ts": "42", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\hooks\\use-logo-src.ts": "43", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\hooks\\use-open-router-models.ts": "44", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\index.ts": "45", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\stats.ts": "46", "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\utils.ts": "47"}, {"size": 808, "mtime": 1753434612445, "results": "48", "hashOfConfig": "49"}, {"size": 20321, "mtime": 1753434612624, "results": "50", "hashOfConfig": "49"}, {"size": 7251, "mtime": 1753434612658, "results": "51", "hashOfConfig": "49"}, {"size": 616, "mtime": 1753434612688, "results": "52", "hashOfConfig": "49"}, {"size": 1730, "mtime": 1753434612507, "results": "53", "hashOfConfig": "49"}, {"size": 3559, "mtime": 1753434612536, "results": "54", "hashOfConfig": "49"}, {"size": 10717, "mtime": 1753434612719, "results": "55", "hashOfConfig": "49"}, {"size": 606, "mtime": 1753434612565, "results": "56", "hashOfConfig": "49"}, {"size": 12387, "mtime": 1753434612752, "results": "57", "hashOfConfig": "49"}, {"size": 471, "mtime": 1753434612780, "results": "58", "hashOfConfig": "49"}, {"size": 11310, "mtime": 1753434612816, "results": "59", "hashOfConfig": "49"}, {"size": 114, "mtime": 1753434612834, "results": "60", "hashOfConfig": "49"}, {"size": 7942, "mtime": 1753434612861, "results": "61", "hashOfConfig": "49"}, {"size": 1099, "mtime": 1753434612890, "results": "62", "hashOfConfig": "49"}, {"size": 872, "mtime": 1753434612917, "results": "63", "hashOfConfig": "49"}, {"size": 9894, "mtime": 1753434612956, "results": "64", "hashOfConfig": "49"}, {"size": 7229, "mtime": 1753434612993, "results": "65", "hashOfConfig": "49"}, {"size": 7862, "mtime": 1753434613025, "results": "66", "hashOfConfig": "49"}, {"size": 1284, "mtime": 1753434613052, "results": "67", "hashOfConfig": "49"}, {"size": 6892, "mtime": 1753434613083, "results": "68", "hashOfConfig": "49"}, {"size": 3764, "mtime": 1753434613112, "results": "69", "hashOfConfig": "49"}, {"size": 5462, "mtime": 1753434613140, "results": "70", "hashOfConfig": "49"}, {"size": 330, "mtime": 1753434613157, "results": "71", "hashOfConfig": "49"}, {"size": 3829, "mtime": 1753434613186, "results": "72", "hashOfConfig": "49"}, {"size": 1929, "mtime": 1753434613212, "results": "73", "hashOfConfig": "49"}, {"size": 6398, "mtime": 1753434613243, "results": "74", "hashOfConfig": "49"}, {"size": 7054, "mtime": 1753434613274, "results": "75", "hashOfConfig": "49"}, {"size": 40, "mtime": 1753434613295, "results": "76", "hashOfConfig": "49"}, {"size": 2232, "mtime": 1753434613324, "results": "77", "hashOfConfig": "49"}, {"size": 545, "mtime": 1753434613356, "results": "78", "hashOfConfig": "49"}, {"size": 1741, "mtime": 1753434613390, "results": "79", "hashOfConfig": "49"}, {"size": 8933, "mtime": 1753434613420, "results": "80", "hashOfConfig": "49"}, {"size": 129, "mtime": 1753434613436, "results": "81", "hashOfConfig": "49"}, {"size": 3672, "mtime": 1753434613466, "results": "82", "hashOfConfig": "49"}, {"size": 795, "mtime": 1753434613494, "results": "83", "hashOfConfig": "49"}, {"size": 2812, "mtime": 1753434613523, "results": "84", "hashOfConfig": "49"}, {"size": 1149, "mtime": 1753434613547, "results": "85", "hashOfConfig": "49"}, {"size": 267, "mtime": 1753434613563, "results": "86", "hashOfConfig": "49"}, {"size": 473, "mtime": 1753434613580, "results": "87", "hashOfConfig": "49"}, {"size": 70, "mtime": 1753434613599, "results": "88", "hashOfConfig": "49"}, {"size": 335, "mtime": 1753434613616, "results": "89", "hashOfConfig": "49"}, {"size": 72, "mtime": 1753434613688, "results": "90", "hashOfConfig": "49"}, {"size": 235, "mtime": 1753434613705, "results": "91", "hashOfConfig": "49"}, {"size": 1981, "mtime": 1753434613722, "results": "92", "hashOfConfig": "49"}, {"size": 131, "mtime": 1753434613632, "results": "93", "hashOfConfig": "49"}, {"size": 3087, "mtime": 1753434613649, "results": "94", "hashOfConfig": "49"}, {"size": 165, "mtime": 1753434613666, "results": "95", "hashOfConfig": "49"}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gytw11", {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\actions\\evals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\enterprise\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\evals\\evals.tsx", [], ["237"], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\evals\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\page.tsx", [], ["238", "239"], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\privacy\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\shell.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\app\\terms\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\animated-text.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\nav-bar.tsx", [], ["240", "241", "242", "243"], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\stats-display.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\chromes\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\enterprise\\contact-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\animated-background.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\code-example.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\company-logos.tsx", [], ["244"], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\faq-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\features-mobile.tsx", [], ["245", "246"], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\features.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\install-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\testimonials-mobile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\testimonials.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\homepage\\whats-new-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\providers\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\providers\\posthog-provider.tsx", [], ["247", "248"], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\providers\\providers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\chart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\scroll-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\components\\ui\\table.tsx", [], ["249", "250"], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\constants.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\format-currency.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\format-duration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\format-score.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\format-tokens.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\hooks\\use-logo-src.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\hooks\\use-open-router-models.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\stats.ts", [], ["251", "252"], "C:\\Users\\<USER>\\Desktop\\Kylo\\apps\\web-roo-code\\src\\lib\\utils.ts", [], [], {"ruleId": "253", "severity": 2, "message": "254", "line": 215, "column": 32, "nodeType": "255", "messageId": "256", "endLine": 215, "endColumn": 35, "suggestions": "257", "suppressions": "258"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 49, "column": 10, "nodeType": "261", "messageId": "262", "endLine": 52, "endColumn": 63, "fix": "263", "suppressions": "264"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 68, "column": 10, "nodeType": "261", "messageId": "262", "endLine": 71, "endColumn": 63, "fix": "265", "suppressions": "266"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 71, "column": 6, "nodeType": "261", "messageId": "262", "endLine": 74, "endColumn": 113, "fix": "267", "suppressions": "268"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 77, "column": 6, "nodeType": "261", "messageId": "262", "endLine": 80, "endColumn": 113, "fix": "269", "suppressions": "270"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 160, "column": 6, "nodeType": "261", "messageId": "262", "endLine": 164, "endColumn": 44, "fix": "271", "suppressions": "272"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 167, "column": 6, "nodeType": "261", "messageId": "262", "endLine": 171, "endColumn": 44, "fix": "273", "suppressions": "274"}, {"ruleId": "275", "severity": 1, "message": "276", "line": 30, "column": 7, "nodeType": "261", "endLine": 34, "endColumn": 9, "suppressions": "277"}, {"ruleId": "253", "severity": 2, "message": "254", "line": 33, "column": 40, "nodeType": "255", "messageId": "256", "endLine": 33, "endColumn": 43, "suggestions": "278", "suppressions": "279"}, {"ruleId": "253", "severity": 2, "message": "254", "line": 38, "column": 42, "nodeType": "255", "messageId": "256", "endLine": 38, "endColumn": 45, "suggestions": "280", "suppressions": "281"}, {"ruleId": "282", "severity": 1, "message": "283", "line": 26, "column": 5, "nodeType": "284", "endLine": 26, "endColumn": 40, "suggestions": "285", "suppressions": "286"}, {"ruleId": "282", "severity": 1, "message": "287", "line": 26, "column": 16, "nodeType": "288", "endLine": 26, "endColumn": 39, "suppressions": "289"}, {"ruleId": "290", "severity": 2, "message": "291", "line": 50, "column": 5, "nodeType": "292", "messageId": "293", "endLine": 50, "endColumn": 14, "suppressions": "294"}, {"ruleId": "290", "severity": 2, "message": "291", "line": 65, "column": 5, "nodeType": "292", "messageId": "293", "endLine": 65, "endColumn": 14, "suppressions": "295"}, {"ruleId": "253", "severity": 2, "message": "254", "line": 50, "column": 31, "nodeType": "255", "messageId": "256", "endLine": 50, "endColumn": 34, "suggestions": "296", "suppressions": "297"}, {"ruleId": "253", "severity": 2, "message": "254", "line": 93, "column": 46, "nodeType": "255", "messageId": "256", "endLine": 93, "endColumn": 49, "suggestions": "298", "suppressions": "299"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["300", "301"], ["302"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "303", "text": "304"}, ["305"], {"range": "306", "text": "304"}, ["307"], {"range": "308", "text": "304"}, ["309"], {"range": "310", "text": "304"}, ["311"], {"range": "312", "text": "304"}, ["313"], {"range": "314", "text": "304"}, ["315"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", ["316"], ["317", "318"], ["319"], ["320", "321"], ["322", "323"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'searchParams'. Either include it or remove the dependency array.", "ArrayExpression", ["324"], ["325"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", ["326"], "react/prop-types", "'className' is missing in props validation", "Property", "missingPropType", ["327"], ["328"], ["329", "330"], ["331"], ["332", "333"], ["334", "335"], {"messageId": "336", "fix": "337", "desc": "338"}, {"messageId": "339", "fix": "340", "desc": "341"}, {"kind": "342", "justification": "343"}, [2179, 2179], " rel=\"noreferrer\"", {"kind": "342", "justification": "343"}, [2877, 2877], {"kind": "342", "justification": "343"}, [2779, 2779], {"kind": "342", "justification": "343"}, [2988, 2988], {"kind": "342", "justification": "343"}, [6524, 6524], {"kind": "342", "justification": "343"}, [6804, 6804], {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"messageId": "336", "fix": "344", "desc": "338"}, {"messageId": "339", "fix": "345", "desc": "341"}, {"kind": "342", "justification": "343"}, {"messageId": "336", "fix": "346", "desc": "338"}, {"messageId": "339", "fix": "347", "desc": "341"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"desc": "348", "fix": "349"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"messageId": "336", "fix": "350", "desc": "338"}, {"messageId": "339", "fix": "351", "desc": "341"}, {"kind": "342", "justification": "343"}, {"messageId": "336", "fix": "352", "desc": "338"}, {"messageId": "339", "fix": "353", "desc": "341"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, "suggestUnknown", {"range": "354", "text": "355"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "356", "text": "357"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", {"range": "358", "text": "355"}, {"range": "359", "text": "357"}, {"range": "360", "text": "355"}, {"range": "361", "text": "357"}, "Update the dependencies array to be: [pathname, searchParams]", {"range": "362", "text": "363"}, {"range": "364", "text": "355"}, {"range": "365", "text": "357"}, {"range": "366", "text": "355"}, {"range": "367", "text": "357"}, [7065, 7068], "unknown", [7065, 7068], "never", [991, 994], [991, 994], [1153, 1156], [1153, 1156], [851, 886], "[pathname, searchParams]", [1221, 1224], [1221, 1224], [2278, 2281], [2278, 2281]]